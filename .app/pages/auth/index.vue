<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { Field, useForm } from 'vee-validate'
import { z } from 'zod'
import { useUserSession } from '~/stores/userSession'
import type { ILoginRequest, ILoginResponse } from '~/interfaces/auth/auth.interface'
// We'll use Google Identity Services directly instead of vue3-google-signin
const { $api } = useNuxtApp()


definePageMeta({
  layout: 'empty',
  title: 'Login',
  name: 'Login',
  preview: {
    title: 'Login',
    description: 'For authentication and sign in',
    categories: ['layouts', 'authentication'],
    src: '/img/screens/auth-login-1.png',
    srcDark: '/img/screens/auth-login-1-dark.png',
    order: 151,
  },
})

const userSession = useUserSession()
const router = useRouter()

const VALIDATION_TEXT = {
  EMAIL_REQUIRED: 'A valid email is required',
  PASSWORD_REQUIRED: 'A password is required',
}

// This is the Zod schema for the form input
// It's used to define the shape that the form data will have
const zodSchema = z.object({
  email: z.string().email(VALIDATION_TEXT.EMAIL_REQUIRED),
  password: z.string().min(1, VALIDATION_TEXT.PASSWORD_REQUIRED),
  trustDevice: z.boolean(),
})

// Zod has a great infer method that will
// infer the shape of the schema into a TypeScript type
type FormInput = z.infer<typeof zodSchema>

const validationSchema = toTypedSchema(zodSchema)
const initialValues = {
  email: '',
  password: '',
  trustDevice: false,
} satisfies FormInput

const {
  handleSubmit,
  isSubmitting,
  setFieldError,
  meta,
  values,
  errors,
  resetForm,
  setFieldValue,
  setErrors,
} = useForm({
  validationSchema,
  initialValues,
})

// Google Auth state
const isGoogleLoading = ref(false)
const isGoogleReady = ref(false)

// Google Auth configuration
const runtimeConfig = useRuntimeConfig()
const GOOGLE_CLIENT_ID = runtimeConfig.public.googleClientId || 'YOUR_GOOGLE_CLIENT_ID' // Replace with your actual client ID

// Initialize Google Sign-In
onMounted(() => {
  console.log('🔥 Initializing Google Sign-In...')
  
  // Load Google Identity Services
  const script = document.createElement('script')
  script.src = 'https://accounts.google.com/gsi/client'
  script.async = true
  script.defer = true
  script.onload = () => {
    console.log('🔥 Google script loaded')
    initializeGoogleSignIn()
  }
  script.onerror = () => {
    console.error('🚨 Failed to load Google script')
  }
  document.head.appendChild(script)
})

const initializeGoogleSignIn = () => {
  if (typeof window !== 'undefined' && window.google?.accounts?.id) {
    console.log('🔥 Initializing Google Sign-In client...')
    console.log('🔥 Using Client ID:', GOOGLE_CLIENT_ID)
    
    try {
      // Clean initialization for ID token flow
      window.google.accounts.id.initialize({
        client_id: GOOGLE_CLIENT_ID,
        callback: handleGoogleResponse,
        auto_select: false,
        cancel_on_tap_outside: true,
      })
      
      isGoogleReady.value = true
      console.log('🔥 Google Sign-In initialized successfully!')
    } catch (error) {
      console.error('🚨 Error initializing Google Sign-In:', error)
    }
  } else {
    console.log('🔥 Google services not ready, retrying...')
    setTimeout(initializeGoogleSignIn, 500)
  }
}

// Handle Google response
const handleGoogleResponse = async (response: any) => {
  console.log('🔥 Google Auth Success - Response:', response)
  
  isGoogleLoading.value = true
  
  try {
    const idToken = response.credential
    console.log('🔥 ID Token received:', !!idToken)
    
    if (!idToken) {
      throw new Error('No ID token found in response')
    }

    console.log('🔥 Sending ID token to backend...')
    
    // Send the ID token to your backend
    const result = await userSession.googleLogin(idToken)

    console.log('🔥 Backend response received:', !!result)

    // Redirect based on user verification status
    if (userSession.isVerified) {
      router.push('/app/dashboard')
    } else {
      router.push('/auth/verify')
    }
  } catch (error: any) {
    console.error('🚨 Google login error:', error)
    // Handle error appropriately
    alert('Google login failed. Please try again.')
  } finally {
    isGoogleLoading.value = false
  }
}

// Regular form submission handler
const onSubmit = handleSubmit(async (values) => {
  // here you have access to the validated form values
  console.log('auth-success', values)

  const loginRequest: ILoginRequest = {
    email: values.email,
    password: values.password,
  }

  try {
    // This is where you would send the form data to the server
    // In this case, we're just setting the user session
    // const response = await $api.auth.login(loginRequest) as ILoginResponse
    await userSession.login(loginRequest) as ILoginResponse

    // This will set the user session and redirect to the dashboard
  }
  catch (error: any) {
    if (error.response.status === 422) {
    // this will set the error on the form
      setFieldError('password', 'Invalid credentials')
      setFieldError('email', 'Invalid credentials')
    }
    return
  }

  // check user status and redirect to the appropriate page
  if (userSession.isVerified) {
    router.push('/app/dashboard')
  }
  else {
    router.push('/auth/verify')
  }
})

// Google login trigger function
const handleGoogleLogin = () => {
  console.log('🔥 Google login button clicked')
  
  if (!GOOGLE_CLIENT_ID || GOOGLE_CLIENT_ID === 'YOUR_GOOGLE_CLIENT_ID') {
    console.error('🚨 Google Client ID not configured!')
    alert('Google Client ID not configured. Please check your environment variables.')
    return
  }
  
  if (!isGoogleReady.value) {
    console.error('🚨 Google Sign-In not ready yet')
    alert('Google Sign-In is not ready yet. Please wait and try again.')
    return
  }
  
  if (isGoogleLoading.value) {
    console.log('🔥 Already loading, ignoring click')
    return
  }
  
  if (!window.google?.accounts?.id) {
    console.error('🚨 Google Identity Services not available')
    alert('Google services not available. Please refresh the page.')
    return
  }
  
  try {
    console.log('🔥 Triggering Google Sign-In popup...')
    isGoogleLoading.value = true
    
    // Use the prompt method which will show the account picker
    window.google.accounts.id.prompt((notification: any) => {
      console.log('🔥 Prompt notification:', notification)
      
      if (notification.isNotDisplayed()) {
        console.log('🔥 One Tap not displayed, reason:', notification.getNotDisplayedReason())
        // Reset loading state if prompt is not displayed
        isGoogleLoading.value = false
        
        // Show a message to user
        alert('Please allow popups for this site and try again, or use the regular login form.')
      } else if (notification.isSkippedMoment()) {
        console.log('🔥 One Tap skipped, reason:', notification.getSkippedReason())
        isGoogleLoading.value = false
      }
    })
    
  } catch (error) {
    console.error('🚨 Error starting Google Sign-In:', error)
    isGoogleLoading.value = false
    alert('Failed to start Google Sign-In. Please try again.')
  }
}


// Add this helper function for popup fallback
const triggerGooglePopup = () => {
  // Create a temporary button element for popup
  const tempDiv = document.createElement('div')
  tempDiv.style.display = 'none'
  document.body.appendChild(tempDiv)
  
  window.google.accounts.id.renderButton(tempDiv, {
    theme: 'outline',
    size: 'large',
    type: 'standard',
    click_listener: () => {
      console.log('🔥 Popup button clicked')
    }
  })
  
  // Trigger click on the hidden button
  const button = tempDiv.querySelector('div[role="button"]') as HTMLElement
  if (button) {
    button.click()
  }
  
  // Clean up
  setTimeout(() => {
    document.body.removeChild(tempDiv)
  }, 1000)
}


// Handle OAuth2 response and get user info
const handleOAuth2Response = async (accessToken: string) => {
  try {
    console.log('🔥 Getting user info with access token...')
    
    // Get user info from Google
    const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    })
    
    if (!userInfoResponse.ok) {
      throw new Error('Failed to get user info')
    }
    
    const userInfo = await userInfoResponse.json()
    console.log('🔥 User Info:', userInfo)
    
    // Create a mock ID token payload (you might need to adjust this based on your backend)
    // In a real implementation, you'd want to send the access token to your backend
    // and have the backend validate it and create the user session
    
    console.log('🔥 Making API call to backend...')
    
    // For now, let's try sending the access token
    // You might need to modify your backend to accept access tokens
    const result = await $fetch('/api/v1/auth/google/login', {
      method: 'POST',
      body: {
        accessToken: accessToken, // Send access token instead
        userInfo: userInfo // Also send user info
      },
    }) as ILoginResponse

    console.log('🔥 Backend response:', result)

    // Set the user session with the response
    await userSession.setSession(result)

    // Redirect based on user verification status
    if (userSession.isVerified) {
      router.push('/app/dashboard')
    } else {
      router.push('/auth/verify')
    }
  } catch (error: any) {
    console.error('🚨 Error processing OAuth2 response:', error)
  } finally {
    isGoogleLoading.value = false
  }
}
</script>

<template>
  <div class="dark:bg-muted-800 flex min-h-screen bg-white">
    <div
      class="relative flex flex-1 flex-col justify-center px-6 py-12 lg:w-2/5 lg:flex-none"
    >
      <div class="dark:bg-muted-800 relative mx-auto w-full max-w-sm bg-white">
        <!--Nav-->
        <div class="flex w-full items-center justify-between">
          <NuxtLink
            to="/explore"
            class="text-muted-400 hover:text-primary-500 flex items-center gap-2 font-sans font-medium transition-colors duration-300"
          >
            <Icon name="gg:arrow-long-left" class="size-5" />
            <span>Back to Explore</span>
          </NuxtLink>
          <!--Theme button-->
          <BaseThemeToggle />
        </div>
        <div>
          <BaseHeading
            as="h2"
            size="3xl"
            lead="relaxed"
            weight="medium"
            class="mt-6"
          >
            Welcome back.
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-400 mb-6">
            Login with social media or your credentials
          </BaseParagraph>
          <!-- Social Sign Up Buttons -->
          <div class="flex flex-wrap justify-between gap-4">
            <!--Google button-->
            <button
              :disabled="!isGoogleReady || isGoogleLoading"
              class="dark:bg-muted-700 text-muted-800 border-muted-300 dark:border-muted-600 nui-focus relative inline-flex grow items-center justify-center gap-2 rounded border bg-white px-6 py-4 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
              @click="handleGoogleLogin"
            >
              <Icon 
                v-if="!isGoogleLoading"
                name="logos:google-icon" 
                class="size-5" 
              />
              <Icon 
                v-else
                name="svg-spinners:90-ring-with-bg" 
                class="size-5" 
              />
              <div>
                {{ isGoogleLoading ? 'Signing in...' : 'Login with Google' }}
              </div>
            </button>
            <!--Facebook button-->
            <button
              class="bg-muted-200 dark:bg-muted-700 hover:bg-muted-100 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 nui-focus w-[calc(50%_-_0.5rem)] cursor-pointer rounded px-5 py-4 text-center transition-colors duration-300 md:w-auto"
            >
              <Icon name="fa6-brands:facebook-f" class="mx-auto size-4" />
            </button>
            <!--Apple button-->
            <button
              class="bg-muted-200 dark:bg-muted-700 hover:bg-muted-100 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 nui-focus w-[calc(50%_-_0.5rem)] cursor-pointer rounded px-5 py-4 text-center transition-colors duration-300 md:w-auto"
            >
              <Icon name="fa6-brands:apple" class="mx-auto size-4" />
            </button>
          </div>
          <!-- 'or' divider -->
          <div class="flex-100 mt-8 flex items-center">
            <hr
              class="border-muted-200 dark:border-muted-700 flex-auto border-t-2"
            >
            <span
              class="text-muted-600 dark:text-muted-300 px-4 font-sans font-light"
            >
              OR
            </span>
            <hr
              class="border-muted-200 dark:border-muted-700 flex-auto border-t-2"
            >
          </div>
        </div>

        <!--Form section-->
        <form
          method="POST"
          action=""
          class="mt-6"
          novalidate
          @submit.prevent="onSubmit"
        >
          <div class="mt-5">
            <div>
              <div class="space-y-4">
                <Field
                  v-slot="{ field, errorMessage, handleChange, handleBlur }"
                  name="email"
                >
                  <BaseInput
                    :model-value="field.value"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                    type="email"
                    label="Email address"
                    placeholder="Email address"
                    autocomplete="email"
                    :classes="{
                      input: 'h-12',
                    }"
                    @update:model-value="handleChange"
                    @blur="handleBlur"
                  />
                </Field>

                <Field
                  v-slot="{ field, errorMessage, handleChange, handleBlur }"
                  name="password"
                >
                  <BaseInput
                    :model-value="field.value"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                    type="password"
                    label="Password"
                    placeholder="Password"
                    autocomplete="current-password"
                    :classes="{
                      input: 'h-12',
                    }"
                    @update:model-value="handleChange"
                    @blur="handleBlur"
                  />
                </Field>
              </div>

              <div class="mt-6 flex items-center justify-between">
                <Field
                  v-slot="{ field, handleChange, handleBlur }"
                  name="trustDevice"
                >
                  <BaseCheckbox
                    :model-value="field.value"
                    :disabled="isSubmitting"
                    rounded="sm"
                    label="Trust for 60 days"
                    color="primary"
                    @update:model-value="handleChange"
                    @blur="handleBlur"
                  />
                </Field>

                <div class="text-xs leading-5">
                  <NuxtLink
                    to="/auth/recover"
                    class="text-primary-600 hover:text-primary-500 font-sans font-medium underline-offset-4 transition duration-150 ease-in-out hover:underline"
                  >
                    Forgot your password?
                  </NuxtLink>
                </div>
              </div>

              <!--Submit-->
              <div class="mt-6">
                <div class="block w-full rounded-md shadow-sm">
                  <BaseButton
                    :disabled="isSubmitting"
                    :loading="isSubmitting"
                    type="submit"
                    color="primary"
                    class="!h-11 w-full"
                  >
                    Sign in
                  </BaseButton>
                </div>
              </div>
            </div>

            <!--No account link-->
            <p
              class="text-muted-400 mt-4 flex justify-between font-sans text-xs leading-5"
            >
              <span>Don't have an account?</span>
              <NuxtLink
                to="/auth/signup"
                class="text-primary-600 hover:text-primary-500 font-medium underline-offset-4 transition duration-150 ease-in-out hover:underline"
              >
                Start your 14-day free trial
              </NuxtLink>
            </p>
          </div>
        </form>
      </div>
    </div>
    <div
      class="relative hidden w-0 flex-1 lg:flex lg:w-3/5"
    >
      <!-- Background Image -->
      <img
        class="h-full w-full object-cover"
        src="https://images.unsplash.com/photo-*************-bbd176166bab?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
        alt="Coworking space interior"
      >

      <!-- Modern Gradient Overlay -->
      <div class="absolute inset-0 bg-gradient-to-br from-primary-500/20 via-transparent to-slate-900/40"></div>

      <!-- Bottom Gradient for Text Readability -->
      <div class="absolute inset-x-0 bottom-0 h-1/3 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>

      <!-- Optional: Subtle Pattern Overlay -->
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.1),transparent_50%)]"></div>

      <!-- Content Overlay (Optional - for future use) -->
      <div class="absolute inset-0 flex items-end justify-start p-12">
        <div class="text-white">
          <h3 class="text-2xl font-semibold mb-2 opacity-90">Space on demand</h3>
          <p class="text-sm opacity-75 max-w-md">Experience the future of coworking with our modern spaces designed for productivity and collaboration.</p>
        </div>
      </div>
    </div>
  </div>
</template>