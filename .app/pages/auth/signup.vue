<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { Field, useForm } from 'vee-validate'
import { z } from 'zod'
import { useUserSession } from '~/stores/userSession'
import type { IRegisterRequest } from '~/interfaces/auth/auth.interface'
import {
  useCodeClient,
  type ImplicitFlowSuccessResponse,
  type ImplicitFlowErrorResponse,
} from "vue3-google-signin";

import { AddonInputPassword } from '#components'

const userSession = useUserSession()

definePageMeta({
  layout: 'empty',
  title: 'Signup',
  preview: {
    title: 'Signup',
    description: 'Join <PERSON><PERSON> and start your journey',
    categories: ['layouts', 'authentication'],
    src: '/img/screens/auth-signup-1.png',
    srcDark: '/img/screens/auth-signup-1-dark.png',
    order: 157,
  },
})

const passwordRef = ref<InstanceType<typeof AddonInputPassword>>()

const VALIDATION_TEXT = {
  EMAIL_REQUIRED: 'A valid email is required',
  FIRSTNAME_LENGTH: 'First name must be at least 3 characters',
  LASTNAME_LENGTH: 'Last name must be at least 3 characters',
  PASSWORD_LENGTH: 'Password must be at least 8 characters',
  PASSWORD_CONTAINS_EMAIL: 'Password cannot contain your email',
  PASSWORD_MATCH: 'Passwords do not match',
}

// This is the Zod schema for the form input
// It's used to define the shape that the form data will have
const zodSchema = z
  .object({
    firstname: z.string().min(3, VALIDATION_TEXT.FIRSTNAME_LENGTH),
    lastname: z.string().min(3, VALIDATION_TEXT.LASTNAME_LENGTH),
    email: z.string().email(VALIDATION_TEXT.EMAIL_REQUIRED),
    password: z.string().min(8, VALIDATION_TEXT.PASSWORD_LENGTH),
    confirmPassword: z.string(),
  })
  .superRefine((data, ctx) => {
    // This is a custom validation function that will be called
    // before the form is submitted
    if (passwordRef.value?.validation?.feedback?.warning || passwordRef.value?.validation?.feedback?.suggestions?.length) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: passwordRef.value?.validation?.feedback?.warning || passwordRef.value.validation.feedback?.suggestions?.[0],
        path: ['password'],
      })
    }
    if (data.password !== data.confirmPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: VALIDATION_TEXT.PASSWORD_MATCH,
        path: ['confirmPassword'],
      })
    }
  })

// Zod has a great infer method that will
// infer the shape of the schema into a TypeScript type
type FormInput = z.infer<typeof zodSchema>

const validationSchema = toTypedSchema(zodSchema)
const initialValues = {
  firstname: '',
  lastname: '',
  email: '',
  password: '',
  confirmPassword: '',
} satisfies FormInput

const { values, handleSubmit, isSubmitting, setFieldError } = useForm({
  validationSchema,
  initialValues,
})

const router = useRouter()
const toaster = useToaster()

// This is where you would send the form data to the server
const onSubmit = handleSubmit(async (values) => {
  // here you have access to the validated form values
  console.log('auth-success', values)

  const registerRequest: IRegisterRequest = {
    firstName: values.firstname,
    lastName: values.lastname,
    email: values.email,
    password: values.password,
  }

  try {

    await userSession.register(registerRequest)

    toaster.clearAll()
    toaster.show({
      title: 'Success',
      message: `Please check your email to verify your account`,
      color: 'success',
      icon: 'ph:user-circle-fill',
      closable: true,
    })
    router.push('/auth')
  }
  catch (error: any) {
    if (error.response.status === 422) {
      setFieldError('email', 'Email already exists')
    }
  }
})

// Google Signin

const handleOnSuccess = async (response: ImplicitFlowSuccessResponse) => {
  // send code to a backend server to verify it.
  console.log("Code: ", response);

  // get id_token from response

  const result = await $api.auth.googleLogin(response.code)
  console.log("Result: ", result);
};

const handleOnError = (errorResponse: ImplicitFlowErrorResponse) => {
  console.log("Error: ", errorResponse);
};

const { isReady, login } = useCodeClient({
  onSuccess: handleOnSuccess,
  onError: handleOnError,
  // other options
});
</script>

<template>
  <div class="h-screen md:flex">
    <div
      class="from-primary-900 to-primary-500 i group relative hidden w-1/2 items-center justify-around overflow-hidden bg-gradient-to-tr md:flex"
    >
      <!-- Logo Branding Area -->
      <div class="absolute top-8 left-8 z-20">
        <div class="flex items-center gap-3 opacity-90 group-hover:opacity-100 transition-all duration-500 group-hover:scale-110">
          <div class="bg-white/15 backdrop-blur-sm rounded-2xl p-3 border border-white/20 group-hover:bg-white/25 group-hover:border-white/30 transition-all duration-500 group-hover:shadow-2xl group-hover:shadow-white/10">
            <TairoLogo class="h-10 w-10 text-white drop-shadow-lg group-hover:drop-shadow-2xl transition-all duration-500" />
          </div>
          <div class="text-white transform translate-x-0 group-hover:translate-x-2 transition-all duration-500">
            <div class="text-lg font-bold tracking-wide">SOD</div>
            <div class="text-xs text-white/80 font-medium">Space on Demand</div>
          </div>
        </div>
      </div>

      <div class="mx-auto max-w-xs text-center relative z-10">
        <BaseHeading
          as="h2"
          size="3xl"
          weight="medium"
          class="text-white transform group-hover:scale-105 transition-all duration-500"
        >
          Have an Account?
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-200 mb-3 transform group-hover:translate-y-1 transition-all duration-500 delay-100">
          No need to waste time on this page, let's take you back to your
          account
        </BaseParagraph>
        <BaseButton
          to="/auth/"
          class="w-full transform group-hover:scale-105 group-hover:shadow-xl transition-all duration-500 delay-200"
        >
          Login to Account
        </BaseButton>
      </div>

      <!-- Coworking Space Elements -->

      <!-- Desk and Chair (Top Left) -->
      <div class="absolute top-12 left-12 opacity-20 group-hover:opacity-40 transition-all duration-700 delay-100 group-hover:scale-110">
        <!-- Desk -->
        <div class="bg-white/20 w-16 h-10 rounded-lg relative">
          <!-- Laptop -->
          <div class="bg-white/30 w-8 h-5 rounded-sm absolute top-1 left-2 group-hover:bg-white/50 transition-all duration-500"></div>
          <!-- Coffee Cup -->
          <div class="bg-white/25 w-3 h-3 rounded-full absolute top-2 right-2 group-hover:bg-white/40 transition-all duration-500"></div>
        </div>
        <!-- Chair -->
        <div class="bg-white/15 w-6 h-8 rounded-t-lg absolute -bottom-6 left-5 group-hover:bg-white/25 transition-all duration-500"></div>
      </div>

      <!-- Plant (Top Right) -->
      <div class="absolute top-16 right-16 opacity-25 group-hover:opacity-50 transition-all duration-600 delay-200 group-hover:scale-125 group-hover:rotate-3">
        <!-- Pot -->
        <div class="bg-white/20 w-8 h-6 rounded-b-full"></div>
        <!-- Plant leaves -->
        <div class="absolute -top-2 left-1">
          <div class="bg-white/30 w-3 h-4 rounded-full transform rotate-12 group-hover:rotate-24 transition-all duration-700"></div>
          <div class="bg-white/25 w-3 h-4 rounded-full transform -rotate-12 absolute top-0 left-3 group-hover:-rotate-24 transition-all duration-700"></div>
          <div class="bg-white/20 w-2 h-3 rounded-full absolute top-1 left-1.5 group-hover:scale-110 transition-all duration-500"></div>
        </div>
      </div>

      <!-- Meeting Table (Bottom Left) -->
      <div class="absolute bottom-16 left-16 opacity-20 group-hover:opacity-40 transition-all duration-800 delay-150 group-hover:scale-105">
        <!-- Table -->
        <div class="bg-white/20 w-20 h-12 rounded-xl relative">
          <!-- Chairs around table -->
          <div class="bg-white/15 w-4 h-3 rounded absolute -top-2 left-2 group-hover:bg-white/25 transition-all duration-500"></div>
          <div class="bg-white/15 w-4 h-3 rounded absolute -top-2 right-2 group-hover:bg-white/25 transition-all duration-500 delay-100"></div>
          <div class="bg-white/15 w-4 h-3 rounded absolute -bottom-2 left-2 group-hover:bg-white/25 transition-all duration-500 delay-200"></div>
          <div class="bg-white/15 w-4 h-3 rounded absolute -bottom-2 right-2 group-hover:bg-white/25 transition-all duration-500 delay-300"></div>
          <!-- Documents on table -->
          <div class="bg-white/25 w-6 h-4 rounded-sm absolute top-2 left-2 group-hover:bg-white/40 transition-all duration-500"></div>
        </div>
      </div>

      <!-- Bookshelf (Bottom Right) -->
      <div class="absolute bottom-12 right-12 opacity-25 group-hover:opacity-45 transition-all duration-700 delay-250 group-hover:scale-110">
        <!-- Shelf structure -->
        <div class="bg-white/20 w-12 h-16 rounded-lg relative">
          <!-- Shelves -->
          <div class="bg-white/30 w-full h-0.5 absolute top-4"></div>
          <div class="bg-white/30 w-full h-0.5 absolute top-8"></div>
          <div class="bg-white/30 w-full h-0.5 absolute top-12"></div>
          <!-- Books -->
          <div class="bg-white/25 w-2 h-3 absolute top-1 left-1 group-hover:bg-white/40 transition-all duration-500"></div>
          <div class="bg-white/20 w-2 h-3 absolute top-1 left-3 group-hover:bg-white/35 transition-all duration-500 delay-100"></div>
          <div class="bg-white/25 w-2 h-3 absolute top-5 left-2 group-hover:bg-white/40 transition-all duration-500 delay-200"></div>
          <div class="bg-white/20 w-2 h-3 absolute top-9 left-1 group-hover:bg-white/35 transition-all duration-500 delay-300"></div>
        </div>
      </div>

      <!-- Floating Work Elements -->
      <div class="absolute top-1/3 right-1/4 opacity-15 group-hover:opacity-30 transition-all duration-1000 delay-400 group-hover:translate-y-2">
        <!-- Floating laptop -->
        <div class="bg-white/20 w-10 h-6 rounded-lg transform rotate-12 group-hover:rotate-6 transition-all duration-700"></div>
      </div>

      <div class="absolute bottom-1/3 left-1/4 opacity-15 group-hover:opacity-30 transition-all duration-1000 delay-500 group-hover:-translate-y-2">
        <!-- Floating coffee cup -->
        <div class="bg-white/20 w-4 h-5 rounded-b-full group-hover:scale-125 transition-all duration-600"></div>
        <!-- Steam -->
        <div class="absolute -top-1 left-1">
          <div class="bg-white/15 w-0.5 h-2 rounded-full opacity-0 group-hover:opacity-60 transition-all duration-500 delay-600"></div>
          <div class="bg-white/10 w-0.5 h-2 rounded-full absolute left-1 opacity-0 group-hover:opacity-40 transition-all duration-500 delay-700"></div>
        </div>
      </div>

      <!-- Grid pattern overlay -->
      <div class="absolute inset-0 opacity-5 group-hover:opacity-10 transition-all duration-1000">
        <div class="grid grid-cols-8 grid-rows-8 h-full w-full gap-4">
          <div v-for="i in 64" :key="i" class="border border-white/20 rounded-sm group-hover:border-white/30 transition-all duration-700" :style="`transition-delay: ${i * 10}ms`"></div>
        </div>
      </div>
    </div>
    <div
      class="dark:bg-muted-900 flex flex-col items-center justify-between bg-white py-10 md:w-1/2"
    >
      <div class="mx-auto flex w-full max-w-xs items-center justify-between">
        <NuxtLink
          to="/dashboards"
          class="text-muted-400 hover:text-primary-500 dark:text-muted-700 dark:hover:text-primary-500 transition-colors duration-300"
        >
          <TairoLogo class="size-10" />
        </NuxtLink>
        <div>
          <BaseThemeToggle />
        </div>
      </div>
      <form
        method="POST"
        action=""
        class="mx-auto w-full max-w-xs"
        novalidate
        @submit.prevent="onSubmit"
      >
        <BaseHeading
          as="h2"
          size="3xl"
          weight="medium"
        >
          Welcome to SOD
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-400 mb-6">
          Let's start by creating you account
        </BaseParagraph>

        <div class="mb-4 space-y-3">
          <!-- First Name and Last Name in one row -->
          <div class="flex gap-3">
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="firstname"
            >
              <BaseInput
                :model-value="field.value"
                :error="errorMessage"
                :disabled="isSubmitting"
                type="text"
                label="First Name"
                placeholder="First Name"
                @update:model-value="handleChange"
                @blur="handleBlur"
              />
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="lastname"
            >
              <BaseInput
                :model-value="field.value"
                :error="errorMessage"
                :disabled="isSubmitting"
                type="text"
                label="Last Name"
                placeholder="Last Name"
                @update:model-value="handleChange"
                @blur="handleBlur"
              />
            </Field>
          </div>
          <Field
            v-slot="{ field, errorMessage, handleChange, handleBlur }"
            name="email"
          >
            <BaseInput
              :model-value="field.value"
              :error="errorMessage"
              :disabled="isSubmitting"
              type="email"
              label="Email"
              placeholder="Email Address"
              @update:model-value="handleChange"
              @blur="handleBlur"
            />
          </Field>
          <Field
            v-slot="{ field, errorMessage, handleChange, handleBlur }"
            name="password"
          >
            <AddonInputPassword
              ref="passwordRef"
              :model-value="field.value"
              :error="errorMessage"
              :disabled="isSubmitting"
              :user-inputs="[values.firstname ?? '', values.email ?? '']"
              label="Passowrd"
              placeholder="Password"
              @update:model-value="handleChange"
              @blur="handleBlur"
            />
          </Field>
          <Field
            v-slot="{ field, errorMessage, handleChange, handleBlur }"
            name="confirmPassword"
          >
            <BaseInput
              :model-value="field.value"
              :error="errorMessage"
              :disabled="isSubmitting"
              type="password"
              placeholder="Confirm password"
              @update:model-value="handleChange"
              @blur="handleBlur"
            />
          </Field>
        </div>
        <BaseButton
          :disabled="isSubmitting"
          :loading="isSubmitting"
          type="submit"
          color="primary"
          class="!h-11 w-full"
        >
          Create Account
        </BaseButton>
              <div class="mb-6 mt-3 grid gap-0 sm:grid-cols-3">
                <hr
                  class="border-muted-200 dark:border-muted-700 mt-3 hidden border-t sm:block"
                >
                <span
                  class="dark:bg-muted-800 text-muted-400 relative top-0.5 bg-white text-center font-sans text-sm"
                >
                  Or continue with
                </span>
                <hr
                  class="border-muted-200 dark:border-muted-700 mt-3 hidden border-t sm:block"
                >
              </div>
              <!--Social signup-->
              <div class="grid grid-cols-3 gap-2">
                <button :disabled="!isReady" @click="() => login()"
                  type="button"
                  class="bg-muted-200 dark:bg-muted-700 hover:bg-muted-100 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 relative inline-flex w-full items-center justify-center rounded px-0 py-3 text-center text-sm font-semibold shadow-sm transition-all duration-300"
                >
                  <Icon name="fa6-brands:google" class="size-5" />
                </button>
                <button
                  type="button"
                  class="bg-muted-200 dark:bg-muted-700 hover:bg-muted-100 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 relative inline-flex w-full items-center justify-center rounded px-0 py-3 text-center text-sm font-semibold shadow-sm transition-all duration-300"
                >
                  <Icon name="fa6-brands:facebook-f" class="size-5" />
                </button>
                <button
                  type="button"
                  class="bg-muted-200 dark:bg-muted-700 hover:bg-muted-100 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 relative inline-flex w-full items-center justify-center rounded px-0 py-3 text-center text-sm font-semibold shadow-sm transition-all duration-300"
                >
                  <Icon name="fa6-brands:apple" class="size-5" />
                </button>
              </div>
        <!--No account link-->
        <p
          class="text-muted-400 mt-4 flex justify-between font-sans text-sm leading-5"
        >
          <span>Have an account?</span>
          <NuxtLink
            to="/auth/"
            class="text-primary-600 hover:text-primary-500 font-medium underline-offset-4 transition duration-150 ease-in-out hover:underline focus:underline focus:outline-none"
          >
            Login here
          </NuxtLink>
        </p>
      </form>
      <div class="text-center">
        <BaseText size="sm" class="text-muted-400">
          © {{ new Date().getFullYear() }} Sod. All rights reserved.
        </BaseText>
      </div>
    </div>
  </div>
</template>
